import { Injectable } from '@nestjs/common'
import { DrizzleService } from '@/common/drizzle/database.provider'
import { dramas, orders, episodes, DramaOnlineStatus } from '@/common/drizzle/schema'
import { PaginationResponse } from '@/common/responses/pagination.response'
import { toResponse } from '@/common/utils/transform.util'
import { and, count, desc, eq, gte, like, asc, SQL, AnyColumn, notInArray } from 'drizzle-orm'
import { DramaRankingRequest, DramaListRequest } from '../requests/douyin.request'
import {
  DramaRankingResponse,
  DramaListResponse,
  DramaDetailResponse,
  EpisodeResponse,
} from '../responses/drama.response'
import { LogicDelete } from '@/constants/system.constants'

interface DramaWithSales {
  id: string
  title: string
  coverVertical: string | null
  coverHorizontal: string | null
  desp: string | null
  recommendation: string | null
  seqNum: number | null
  year: number | null
  tagList: string | null
  categoryId: string | null
  albumStatus: number | null
  originalPrice: string
  discountPrice: string
  freePreview: number | null
  currency: string
  salesCount: number
}

interface CountResult {
  count: number
}

interface DramaListData {
  id: string
  title: string
  coverVertical: string | null
  coverHorizontal: string | null
  desp: string | null
  recommendation: string | null
  seqNum: number | null
  year: number | null
  tagList: string | null
  categoryId: string | null
  albumStatus: number | null
  originalPrice: string
  discountPrice: string
  freePreview: number | null
  currency: string
}

interface DramaDetailData {
  id: string
  title: string
  coverVertical: string | null
  coverHorizontal: string | null
  desp: string | null
  summary: string | null
  recommendation: string | null
  seqNum: number | null
  year: number | null
  tagList: string | null
  categoryId: string | null
  albumStatus: number | null
  originalPrice: string
  discountPrice: string
  freePreview: number | null
  currency: string
  authorId: string | null
  introImages: string | null
  director: string | null
  actor: string | null
  duration: number | null
}

interface EpisodeData {
  id: string
  title: string
  seq: number
  coverUrl: string | null
  videoUrl: string
  videoOrientation: number | null
  description: string | null
  duration: string | null
  isFree: number | null
  price: string
  viewCount: number | null
  playbackCount: number | null
}

@Injectable()
export class DouyinDramaService {
  constructor(private readonly drizzle: DrizzleService) {}

  /**
   * 获取短剧排行榜
   * @param request 排行榜请求参数
   * @returns 分页的排行榜数据
   */
  async getDramaRanking(request: DramaRankingRequest): Promise<PaginationResponse<DramaRankingResponse>> {
    const { type = 'hot', page = 1, size = 20 } = request
    const offset = (page - 1) * size

    // 构建基础查询条件：只查询已上线的短剧
    const baseCondition = and(
      eq(dramas.isDeleted, LogicDelete.NotDeleted),
      eq(dramas.onlineStatus, DramaOnlineStatus.ONLINE),
    )

    let salesQuery: Promise<DramaWithSales[]>
    let totalQuery: Promise<CountResult[]>

    if (type === 'hot') {
      // 热播榜：近3个月的销量排行
      const threeMonthsAgo = new Date()
      threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3)

      // 查询近3个月的销量数据
      salesQuery = this.drizzle.db
        .select({
          id: dramas.id,
          title: dramas.title,
          coverVertical: dramas.coverVertical,
          coverHorizontal: dramas.coverHorizontal,
          desp: dramas.desp,
          recommendation: dramas.recommendation,
          seqNum: dramas.seqNum,
          year: dramas.year,
          tagList: dramas.tagList,
          categoryId: dramas.categoryId,
          albumStatus: dramas.albumStatus,
          originalPrice: dramas.originalPrice,
          discountPrice: dramas.discountPrice,
          freePreview: dramas.freePreview,
          currency: dramas.currency,
          salesCount: count(orders.id).as('salesCount'),
        })
        .from(dramas)
        .leftJoin(
          orders,
          and(
            eq(orders.dramaId, dramas.id),
            eq(orders.status, 'paid'),
            eq(orders.isDeleted, LogicDelete.NotDeleted),
            gte(orders.createdAt, threeMonthsAgo),
          ),
        )
        .where(baseCondition)
        .groupBy(dramas.id)
        .orderBy(desc(count(orders.id)))
        .limit(size)
        .offset(offset) as Promise<DramaWithSales[]>

      // 统计总数
      totalQuery = this.drizzle.db
        .select({
          count: count(),
        })
        .from(dramas)
        .where(baseCondition) as Promise<CountResult[]>
    } else {
      // 必看榜：历史销量排行
      salesQuery = this.drizzle.db
        .select({
          id: dramas.id,
          title: dramas.title,
          coverVertical: dramas.coverVertical,
          coverHorizontal: dramas.coverHorizontal,
          desp: dramas.desp,
          recommendation: dramas.recommendation,
          seqNum: dramas.seqNum,
          year: dramas.year,
          tagList: dramas.tagList,
          categoryId: dramas.categoryId,
          albumStatus: dramas.albumStatus,
          originalPrice: dramas.originalPrice,
          discountPrice: dramas.discountPrice,
          freePreview: dramas.freePreview,
          currency: dramas.currency,
          salesCount: count(orders.id).as('salesCount'),
        })
        .from(dramas)
        .leftJoin(orders, and(eq(orders.dramaId, dramas.id), eq(orders.status, 'paid'), eq(orders.isDeleted, 0)))
        .where(baseCondition)
        .groupBy(dramas.id)
        .orderBy(desc(count(orders.id)))
        .limit(size)
        .offset(offset) as Promise<DramaWithSales[]>

      // 统计总数
      totalQuery = this.drizzle.db
        .select({
          count: count(),
        })
        .from(dramas)
        .where(baseCondition) as Promise<CountResult[]>
    }

    // 执行查询
    const [salesData, totalResult] = await Promise.all([salesQuery, totalQuery])

    const total = totalResult[0]?.count ?? 0

    // 添加排名信息
    const rankedData = salesData.map((item, index) => ({
      ...item,
      rank: offset + index + 1,
    }))

    // 转换为响应格式
    const list = toResponse(DramaRankingResponse, rankedData)

    return PaginationResponse.from(list, total, page, size)
  }

  /**
   * 获取短剧列表
   * @param request 查询请求参数
   * @returns 分页的短剧列表数据
   */
  async getDramaList(request: DramaListRequest): Promise<PaginationResponse<DramaListResponse>> {
    const {
      categoryId,
      title,
      albumStatus,
      year,
      tagList,
      freePreview,
      authorId,
      similarTo,
      excludeIds,
      orderBy = 'createdAt',
      order = 'desc',
      page = 1,
      size = 20,
    } = request

    const offset = (page - 1) * size

    // 构建查询条件
    const conditions: SQL[] = [
      eq(dramas.isDeleted, LogicDelete.NotDeleted),
      eq(dramas.onlineStatus, DramaOnlineStatus.ONLINE), // 只查询已上线的短剧
    ]

    // 添加查询条件
    if (categoryId) {
      conditions.push(eq(dramas.categoryId, categoryId))
    }

    if (title) {
      conditions.push(like(dramas.title, `%${title}%`))
    }

    if (albumStatus !== undefined) {
      conditions.push(eq(dramas.albumStatus, albumStatus))
    }

    if (year !== undefined) {
      conditions.push(eq(dramas.year, year))
    }

    if (tagList) {
      conditions.push(like(dramas.tagList, `%${tagList}%`))
    }

    if (freePreview !== undefined) {
      conditions.push(eq(dramas.freePreview, freePreview))
    }

    // 作者ID查询
    if (authorId) {
      conditions.push(eq(dramas.authorId, authorId))
    }

    // 相似推荐查询
    if (similarTo) {
      // 先查询目标短剧的scopeList
      const targetDrama = await this.drizzle.db
        .select({ scopeList: dramas.scopeList })
        .from(dramas)
        .where(eq(dramas.id, similarTo))
        .limit(1)

      if (targetDrama.length > 0 && targetDrama[0].scopeList) {
        // 基于scopeList进行模糊匹配
        conditions.push(like(dramas.scopeList, `%${targetDrama[0].scopeList}%`))
      }
    }

    // 排除指定的短剧ID
    if (excludeIds) {
      const excludeIdArray = excludeIds
        .split(',')
        .map((id) => id.trim())
        .filter((id) => id)
      if (excludeIdArray.length > 0) {
        conditions.push(notInArray(dramas.id, excludeIdArray))
      }
    }

    const whereCondition = and(...conditions)

    // 构建排序
    let orderColumn: AnyColumn
    switch (orderBy) {
      case 'year':
        orderColumn = dramas.year
        break
      case 'seqNum':
        orderColumn = dramas.seqNum
        break
      case 'originalPrice':
        orderColumn = dramas.originalPrice
        break
      default:
        orderColumn = dramas.createdAt
    }
    const orderFunction = order === 'asc' ? asc : desc

    // 查询数据
    const dataQuery = this.drizzle.db
      .select({
        id: dramas.id,
        title: dramas.title,
        coverVertical: dramas.coverVertical,
        coverHorizontal: dramas.coverHorizontal,
        desp: dramas.desp,
        recommendation: dramas.recommendation,
        seqNum: dramas.seqNum,
        year: dramas.year,
        tagList: dramas.tagList,
        categoryId: dramas.categoryId,
        albumStatus: dramas.albumStatus,
        originalPrice: dramas.originalPrice,
        discountPrice: dramas.discountPrice,
        freePreview: dramas.freePreview,
        currency: dramas.currency,
      })
      .from(dramas)
      .where(whereCondition)
      .orderBy(orderFunction(orderColumn))
      .limit(size)
      .offset(offset) as Promise<DramaListData[]>

    // 统计总数
    const totalQuery = this.drizzle.db
      .select({
        count: count(),
      })
      .from(dramas)
      .where(whereCondition) as Promise<CountResult[]>

    // 执行查询
    const [dramaData, totalResult] = await Promise.all([dataQuery, totalQuery])

    const total = totalResult[0]?.count ?? 0

    // 转换为响应格式
    const list = toResponse(DramaListResponse, dramaData)

    return PaginationResponse.from(list, total, page, size)
  }

  /**
   * 获取短剧详情
   * @param id 短剧ID
   * @returns 短剧详情数据
   */
  async getDramaDetail(id: string): Promise<DramaDetailResponse> {
    // 查询短剧基本信息
    const dramaQuery = this.drizzle.db
      .select({
        id: dramas.id,
        title: dramas.title,
        coverVertical: dramas.coverVertical,
        coverHorizontal: dramas.coverHorizontal,
        desp: dramas.desp,
        summary: dramas.summary,
        recommendation: dramas.recommendation,
        seqNum: dramas.seqNum,
        year: dramas.year,
        tagList: dramas.tagList,
        categoryId: dramas.categoryId,
        albumStatus: dramas.albumStatus,
        originalPrice: dramas.originalPrice,
        discountPrice: dramas.discountPrice,
        freePreview: dramas.freePreview,
        currency: dramas.currency,
        authorId: dramas.authorId,
        introImages: dramas.introImages,
        director: dramas.director,
        actor: dramas.actor,
        duration: dramas.duration,
      })
      .from(dramas)
      .where(
        and(
          eq(dramas.id, id),
          eq(dramas.isDeleted, LogicDelete.NotDeleted),
          eq(dramas.onlineStatus, DramaOnlineStatus.ONLINE),
        ),
      ) as Promise<DramaDetailData[]>

    // 查询剧集列表
    const episodesQuery = this.drizzle.db
      .select({
        id: episodes.id,
        title: episodes.title,
        seq: episodes.seq,
        coverUrl: episodes.coverUrl,
        videoUrl: episodes.videoUrl,
        videoOrientation: episodes.videoOrientation,
        description: episodes.description,
        duration: episodes.duration,
        isFree: episodes.isFree,
        price: episodes.price,
        viewCount: episodes.viewCount,
        playbackCount: episodes.playbackCount,
      })
      .from(episodes)
      .where(and(eq(episodes.dramaId, id), eq(episodes.isDeleted, LogicDelete.NotDeleted)))
      .orderBy(asc(episodes.seq)) as Promise<EpisodeData[]>

    // 执行查询
    const [dramaResult, episodesResult] = await Promise.all([dramaQuery, episodesQuery])

    if (!dramaResult || dramaResult.length === 0) {
      throw new Error('短剧不存在或已下线')
    }

    const dramaData = dramaResult[0]

    // 转换为响应格式
    const dramaDetail = toResponse(DramaDetailResponse, dramaData)
    const episodesList = toResponse(EpisodeResponse, episodesResult)

    // 设置剧集列表
    dramaDetail.episodes = episodesList

    return dramaDetail
  }
}
