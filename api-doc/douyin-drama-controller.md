# DouyinDramaController API 测试文档

## 概述
抖音小程序短剧相关API接口，提供短剧排行榜、短剧列表查询和短剧详情获取功能。

**基础路径**: `/dy/drama`

**通用响应格式**:
```json
{
  "code": 0,           // 响应码，0表示成功
  "message": "ok",     // 响应消息
  "data": {}          // 响应数据
}
```

---

## 1. 获取短剧排行榜

### 接口信息
- **URL**: `GET /dy/drama/ranking`
- **描述**: 获取短剧排行榜，支持热播榜（近3个月销量）和必看榜（历史销量）

### 请求参数 (Query Parameters)

| 参数名 | 类型 | 必填 | 默认值 | 描述 | 可选值 |
|--------|------|------|--------|------|--------|
| type | string | 否 | "hot" | 排行榜类型 | "hot"(热播榜), "must_watch"(必看榜) |
| page | number | 否 | 1 | 页码，最小值为1 | 正整数 |
| size | number | 否 | 20 | 每页数量，最小值为1，最大值为50 | 1-50 |

### 请求示例

#### 获取热播榜第一页
```bash
GET /dy/drama/ranking?type=hot&page=1&size=20
```

#### 获取必看榜第二页
```bash
GET /dy/drama/ranking?type=must_watch&page=2&size=10
```

#### 完整请求JSON示例
```json
{
  "type": "hot",
  "page": 1,
  "size": 20
}
```

### 响应数据

#### 成功响应 (200)
```json
{
  "code": 0,
  "message": "ok",
  "data": [
    {
      "id": "2dHdj8KLMnpqr9X",                    // 短剧ID
      "title": "霸道总裁爱上我",                   // 短剧标题
      "coverVertical": "https://example.com/cover1.jpg",  // 竖版封面图片URL
      "coverHorizontal": "https://example.com/cover2.jpg", // 横版封面图片URL
      "desp": "一个灰姑娘与霸道总裁的爱情故事",      // 短剧简介
      "recommendation": "年度必看爱情剧",           // 推荐语
      "seqNum": 30,                              // 总集数
      "year": 2024,                              // 发行年份
      "tagList": "都市,爱情,霸总",                 // 题材标签
      "categoryId": "cat_001",                   // 分类ID
      "albumStatus": 3,                          // 短剧状态: 1-未上映, 2-更新中, 3-已完结
      "rank": 1                                  // 排名
    }
  ]
}
```

#### 字段说明
- **id**: 短剧唯一标识符
- **title**: 短剧标题
- **coverVertical**: 竖版封面图片URL，适用于手机端显示
- **coverHorizontal**: 横版封面图片URL，适用于横屏显示
- **desp**: 短剧简介描述
- **recommendation**: 推荐语，一句话推荐
- **seqNum**: 短剧总集数
- **year**: 发行年份
- **tagList**: 题材标签，多个标签用逗号分隔
- **categoryId**: 分类ID
- **albumStatus**: 短剧状态
  - 1: 未上映
  - 2: 更新中  
  - 3: 已完结
- **rank**: 在排行榜中的排名

---

## 2. 获取短剧列表

### 接口信息
- **URL**: `GET /dy/drama/list`
- **描述**: 根据分类、标题、状态等条件查询短剧列表，支持分页和排序

### 请求参数 (Query Parameters)

| 参数名 | 类型 | 必填 | 默认值 | 描述 | 可选值/限制 |
|--------|------|------|--------|------|-------------|
| categoryId | string | 否 | - | 分类ID | 字符串 |
| title | string | 否 | - | 短剧标题（模糊搜索） | 字符串 |
| albumStatus | number | 否 | - | 短剧状态 | 1(未上映), 2(更新中), 3(已完结) |
| year | number | 否 | - | 发行年份 | 正整数 |
| excludeIds | string | 否 | - | 排除的短剧ID列表（逗号分隔） | 如: "id1,id2,id3" |
| freePreview | number | 否 | - | 是否免费试看 | 0(否), 1(是) |
| orderBy | string | 否 | "createdAt" | 排序字段 | "createdAt", "year", "seqNum", "originalPrice" |
| order | string | 否 | "desc" | 排序方式 | "asc"(升序), "desc"(降序) |
| page | number | 否 | 1 | 页码，最小值为1 | 正整数 |
| size | number | 否 | 20 | 每页数量，最小值为1，最大值为50 | 1-50 |

### 请求示例

#### 基础查询
```bash
GET /dy/drama/list?page=1&size=20
```

#### 按分类查询
```bash
GET /dy/drama/list?categoryId=cat_001&page=1&size=10
```

#### 按标题模糊搜索
```bash
GET /dy/drama/list?title=霸道总裁&page=1&size=15
```

#### 复合条件查询
```bash
GET /dy/drama/list?categoryId=cat_001&albumStatus=3&year=2024&freePreview=1&orderBy=year&order=desc&page=1&size=20
```

#### 完整请求JSON示例
```json
{
  "categoryId": "cat_001",
  "title": "霸道总裁",
  "albumStatus": 3,
  "year": 2024,
  "excludeIds": "id1,id2,id3",
  "freePreview": 1,
  "orderBy": "year",
  "order": "desc",
  "page": 1,
  "size": 20
}
```

### 响应数据

#### 成功响应 (200)
```json
{
  "code": 0,
  "message": "ok",
  "data": [
    {
      "id": "2dHdj8KLMnpqr9X",                    // 短剧ID
      "title": "霸道总裁爱上我",                   // 短剧标题
      "coverVertical": "https://example.com/cover1.jpg",  // 竖版封面图片URL
      "coverHorizontal": "https://example.com/cover2.jpg", // 横版封面图片URL
      "desp": "一个灰姑娘与霸道总裁的爱情故事",      // 短剧简介
      "recommendation": "年度必看爱情剧",           // 推荐语
      "seqNum": 30,                              // 总集数
      "year": 2024,                              // 发行年份
      "tagList": "都市,爱情,霸总",                 // 题材标签
      "categoryId": "cat_001",                   // 分类ID
      "albumStatus": 3,                          // 短剧状态: 1-未上映, 2-更新中, 3-已完结
      "originalPrice": "29.90",                  // 原价
      "discountPrice": "19.90",                  // 优惠价
      "freePreview": 1,                          // 是否免费试看: 0-否, 1-是
      "currency": "CNY"                          // 货币单位
    }
  ]
}
```

#### 字段说明
- **id**: 短剧唯一标识符
- **title**: 短剧标题
- **coverVertical**: 竖版封面图片URL
- **coverHorizontal**: 横版封面图片URL
- **desp**: 短剧简介描述
- **recommendation**: 推荐语
- **seqNum**: 短剧总集数
- **year**: 发行年份
- **tagList**: 题材标签，多个标签用逗号分隔
- **categoryId**: 分类ID
- **albumStatus**: 短剧状态
  - 1: 未上映
  - 2: 更新中
  - 3: 已完结
- **originalPrice**: 原价（字符串格式）
- **discountPrice**: 优惠价（字符串格式）
- **freePreview**: 是否免费试看
  - 0: 否
  - 1: 是
- **currency**: 货币单位（如: CNY, USD, EUR）

---

## 3. 获取短剧详情

### 接口信息
- **URL**: `GET /dy/drama/:id`
- **描述**: 根据短剧ID获取短剧详细信息，包含剧集列表

### 路径参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | string | 是 | 短剧ID |

### 请求示例

```bash
GET /dy/drama/2dHdj8KLMnpqr9X
```

### 响应数据

#### 成功响应 (200)
```json
{
  "code": 0,
  "message": "ok",
  "data": {
    "id": "2dHdj8KLMnpqr9X",                    // 短剧ID
    "title": "霸道总裁爱上我",                   // 短剧标题
    "coverVertical": "https://example.com/cover1.jpg",  // 竖版封面图片URL
    "coverHorizontal": "https://example.com/cover2.jpg", // 横版封面图片URL
    "desp": "一个灰姑娘与霸道总裁的爱情故事",      // 短剧简介
    "summary": "女主角林小雨是一个普通的上班族，偶然机会遇到了霸道总裁陈天明...", // 内容梗概
    "recommendation": "年度必看爱情剧",           // 推荐语
    "seqNum": 30,                              // 总集数
    "year": 2024,                              // 发行年份
    "tagList": "都市,爱情,霸总",                 // 题材标签
    "categoryId": "cat_001",                   // 分类ID
    "albumStatus": 3,                          // 短剧状态: 1-未上映, 2-更新中, 3-已完结
    "originalPrice": "29.90",                  // 原价
    "discountPrice": "19.90",                  // 优惠价
    "freePreview": 1,                          // 是否免费试看: 0-否, 1-是
    "currency": "CNY",                         // 货币单位
    "authorId": "author_001",                  // 作者ID
    "introImages": "https://example.com/intro1.jpg,https://example.com/intro2.jpg", // 剧集介绍图片
    "director": "张导演",                       // 导演
    "actor": "李演员,王演员",                   // 演员
    "duration": 15,                            // 平均单集时长（分钟）
    "episodes": [                              // 剧集列表
      {
        "id": "ep_001",                        // 剧集ID
        "title": "第1集：初次相遇",              // 剧集标题
        "seq": 1,                              // 第几集
        "coverUrl": "https://example.com/ep1_cover.jpg", // 分集封面图片URL
        "videoUrl": "https://example.com/ep1_video.mp4", // 分集视频地址
        "videoOrientation": 1,                 // 视频方向: 1-竖版, 2-横版
        "description": "女主角与男主角的初次相遇", // 分集简介
        "duration": "15:30",                   // 分集时长
        "isFree": 1,                           // 是否免费: 0-付费, 1-免费
        "price": "0.00",                       // 价格
        "viewCount": 1250,                     // 观看次数
        "playbackCount": 980,                  // 播放次数
        "dramaId": "2dHdj8KLMnpqr9X",         // 所属短剧ID
        "douyinEpisodeId": "dy_ep_001"         // 抖音剧集ID
      },
      {
        "id": "ep_002",
        "title": "第2集：误会产生",
        "seq": 2,
        "coverUrl": "https://example.com/ep2_cover.jpg",
        "videoUrl": "https://example.com/ep2_video.mp4",
        "videoOrientation": 1,
        "description": "因为一个误会，两人关系变得紧张",
        "duration": "14:45",
        "isFree": 0,
        "price": "1.99",
        "viewCount": 890,
        "playbackCount": 720,
        "dramaId": "2dHdj8KLMnpqr9X",
        "douyinEpisodeId": "dy_ep_002"
      }
    ]
  }
}
```

#### 字段说明

**短剧基本信息**:
- **id**: 短剧唯一标识符
- **title**: 短剧标题
- **coverVertical**: 竖版封面图片URL
- **coverHorizontal**: 横版封面图片URL
- **desp**: 短剧简介描述
- **summary**: 内容梗概，比简介更详细
- **recommendation**: 推荐语
- **seqNum**: 短剧总集数
- **year**: 发行年份
- **tagList**: 题材标签，多个标签用逗号分隔
- **categoryId**: 分类ID
- **albumStatus**: 短剧状态
  - 1: 未上映
  - 2: 更新中
  - 3: 已完结
- **originalPrice**: 原价（字符串格式）
- **discountPrice**: 优惠价（字符串格式）
- **freePreview**: 是否免费试看
  - 0: 否
  - 1: 是
- **currency**: 货币单位
- **authorId**: 作者/创作者ID
- **introImages**: 剧集介绍图片，多个URL用逗号分隔
- **director**: 导演姓名
- **actor**: 演员姓名，多个演员用逗号分隔
- **duration**: 平均单集时长（分钟）

**剧集信息 (episodes)**:
- **id**: 剧集唯一标识符
- **title**: 剧集标题
- **seq**: 集数序号
- **coverUrl**: 分集封面图片URL
- **videoUrl**: 分集视频地址
- **videoOrientation**: 视频方向
  - 1: 竖版
  - 2: 横版
- **description**: 分集简介
- **duration**: 分集时长（格式: "mm:ss"）
- **isFree**: 是否免费
  - 0: 付费
  - 1: 免费
- **price**: 单集价格（字符串格式）
- **viewCount**: 观看次数
- **playbackCount**: 播放次数
- **dramaId**: 所属短剧ID
- **douyinEpisodeId**: 抖音平台的剧集ID

---

## 错误响应

### 常见错误码

| 错误码 | 描述 | 示例响应 |
|--------|------|----------|
| 400 | 请求参数错误 | `{"code": 400, "message": "参数验证失败", "data": null}` |
| 404 | 资源不存在 | `{"code": 404, "message": "短剧不存在", "data": null}` |
| 500 | 服务器内部错误 | `{"code": 500, "message": "服务器内部错误", "data": null}` |

### 参数验证错误示例

```json
{
  "code": 400,
  "message": "参数验证失败",
  "data": {
    "errors": [
      {
        "field": "type",
        "message": "排行榜类型必须是hot或must_watch"
      },
      {
        "field": "page",
        "message": "页码必须大于0"
      }
    ]
  }
}
```

---

## 测试用例

### 1. 排行榜接口测试

#### 测试用例1: 获取热播榜
```bash
curl -X GET "http://localhost:3000/dy/drama/ranking?type=hot&page=1&size=10"
```

#### 测试用例2: 获取必看榜
```bash
curl -X GET "http://localhost:3000/dy/drama/ranking?type=must_watch&page=1&size=5"
```

#### 测试用例3: 参数验证测试
```bash
curl -X GET "http://localhost:3000/dy/drama/ranking?type=invalid&page=0&size=100"
```

### 2. 短剧列表接口测试

#### 测试用例1: 基础列表查询
```bash
curl -X GET "http://localhost:3000/dy/drama/list?page=1&size=20"
```

#### 测试用例2: 条件筛选查询
```bash
curl -X GET "http://localhost:3000/dy/drama/list?categoryId=cat_001&albumStatus=3&freePreview=1&page=1&size=10"
```

#### 测试用例3: 搜索查询
```bash
curl -X GET "http://localhost:3000/dy/drama/list?title=霸道总裁&orderBy=year&order=desc&page=1&size=15"
```

### 3. 短剧详情接口测试

#### 测试用例1: 获取短剧详情
```bash
curl -X GET "http://localhost:3000/dy/drama/2dHdj8KLMnpqr9X"
```

#### 测试用例2: 不存在的短剧ID
```bash
curl -X GET "http://localhost:3000/dy/drama/invalid_id"
```

---

## 注意事项

1. **分页参数**: page从1开始，size最大值为50
2. **排序字段**: orderBy支持的字段有限，请参考接口文档
3. **ID格式**: 短剧ID通常为KSUID格式的字符串
4. **图片URL**: 所有图片URL都是完整的HTTP/HTTPS地址
5. **价格格式**: 价格字段为字符串格式，保留两位小数
6. **标签格式**: tagList为逗号分隔的字符串
7. **时长格式**: duration字段在短剧详情中为数字（分钟），在剧集中为字符串（mm:ss格式）
8. **枚举值说明**:
   - **albumStatus**: 1=未上映, 2=更新中, 3=已完结
   - **freePreview**: 0=否, 1=是
   - **isFree**: 0=付费, 1=免费
   - **videoOrientation**: 1=竖版, 2=横版

---

## 更新日志

- **v1.0.0** (2024-08-04): 初始版本，包含排行榜、列表查询和详情获取功能
